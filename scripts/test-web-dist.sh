#!/bin/bash

# 测试 web-dist 分支构建和获取流程
# 用于验证 CI/CD 流程和 manage.sh 的修改

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;36m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 测试构建并推送到 web-dist 分支
test_build_and_push() {
    print_info "开始测试构建和推送流程..."
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] || [ ! -d "web/admin-spa" ]; then
        print_error "请在项目根目录运行此脚本"
        return 1
    fi
    
    # 构建前端
    print_info "构建前端..."
    cd web/admin-spa
    
    # 检查 node_modules
    if [ ! -d "node_modules" ]; then
        print_info "安装前端依赖..."
        npm install
    fi
    
    # 执行构建
    npm run build
    
    if [ ! -d "dist" ]; then
        print_error "构建失败，dist 目录不存在"
        cd ../..
        return 1
    fi
    
    print_success "前端构建成功"
    cd ../..
    
    # 创建临时目录保存构建产物
    TEMP_DIR=$(mktemp -d)
    print_info "复制构建产物到临时目录: $TEMP_DIR"
    cp -r web/admin-spa/dist/* "$TEMP_DIR/"
    
    # 配置 git
    git config user.name "Test User"
    git config user.email "<EMAIL>"
    
    # 检查 web-dist 分支是否存在
    print_info "检查 web-dist 分支..."
    if git ls-remote --heads origin web-dist | grep -q web-dist; then
        print_info "web-dist 分支已存在，获取最新版本"
        git fetch origin web-dist:web-dist
        git checkout web-dist
    else
        print_info "创建新的 web-dist 分支"
        git checkout --orphan web-dist
    fi
    
    # 清空当前目录（保留 .git）
    git rm -rf . 2>/dev/null || true
    
    # 复制构建产物
    cp -r "$TEMP_DIR"/* .
    
    # 添加 README
    cat > README.md << EOF
# Claude Relay Service - Web Frontend Build

This branch contains the pre-built frontend assets for Claude Relay Service.

**DO NOT EDIT FILES IN THIS BRANCH DIRECTLY**

These files are automatically generated by the CI/CD pipeline.

Test Build Date: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
EOF
    
    # 提交
    git add -A
    git commit -m "test: frontend build test $(date +%Y%m%d%H%M%S)"
    
    print_success "本地 web-dist 分支创建成功"
    print_warning "注意：这只是本地测试，没有推送到远程仓库"
    print_info "如需推送，请运行: git push origin web-dist --force"
    
    # 切换回主分支
    git checkout main
    
    # 清理临时目录
    rm -rf "$TEMP_DIR"
    
    print_success "测试完成"
}

# 测试从 web-dist 分支获取文件
test_fetch_from_web_dist() {
    print_info "测试从 web-dist 分支获取文件..."
    
    # 创建测试目录
    TEST_DIR="test-web-dist-fetch"
    rm -rf "$TEST_DIR"
    mkdir -p "$TEST_DIR"
    
    # 检查远程 web-dist 分支
    if ! git ls-remote --heads origin web-dist | grep -q web-dist; then
        print_warning "远程 web-dist 分支不存在"
        print_info "尝试使用本地 web-dist 分支..."
        
        # 检查本地分支
        if ! git branch | grep -q web-dist; then
            print_error "本地和远程都没有 web-dist 分支"
            rm -rf "$TEST_DIR"
            return 1
        fi
    fi
    
    print_info "克隆 web-dist 分支到测试目录..."
    
    # 创建临时目录用于 clone
    TEMP_CLONE_DIR=$(mktemp -d)
    
    # 获取仓库 URL
    REPO_URL=$(git config --get remote.origin.url)
    
    # 克隆 web-dist 分支
    if git clone --depth 1 --branch web-dist --single-branch "$REPO_URL" "$TEMP_CLONE_DIR" 2>/dev/null; then
        print_success "成功克隆 web-dist 分支"
        
        # 复制文件（排除 .git 和 README.md）
        if command -v rsync >/dev/null 2>&1; then
            rsync -av --exclude='.git' --exclude='README.md' "$TEMP_CLONE_DIR/" "$TEST_DIR/"
        else
            cp -r "$TEMP_CLONE_DIR"/* "$TEST_DIR/" 2>/dev/null
            rm -rf "$TEST_DIR/.git" 2>/dev/null
            rm -f "$TEST_DIR/README.md" 2>/dev/null
        fi
        
        print_success "文件复制成功"
        print_info "测试目录内容:"
        ls -la "$TEST_DIR" | head -10
        
        # 验证关键文件
        if [ -f "$TEST_DIR/index.html" ]; then
            print_success "✓ index.html 文件存在"
        else
            print_error "✗ index.html 文件不存在"
        fi
        
        if [ -d "$TEST_DIR/assets" ]; then
            print_success "✓ assets 目录存在"
        else
            print_error "✗ assets 目录不存在"
        fi
        
    else
        print_error "克隆 web-dist 分支失败"
        print_info "可能需要先运行: test_build_and_push"
    fi
    
    # 清理
    rm -rf "$TEMP_CLONE_DIR"
    rm -rf "$TEST_DIR"
    
    print_success "获取测试完成"
}

# 显示帮助
show_help() {
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  build   - 测试构建并创建本地 web-dist 分支"
    echo "  fetch   - 测试从 web-dist 分支获取文件"
    echo "  all     - 运行所有测试"
    echo "  help    - 显示帮助"
    echo ""
}

# 主函数
main() {
    case "$1" in
        build)
            test_build_and_push
            ;;
        fetch)
            test_fetch_from_web_dist
            ;;
        all)
            test_build_and_push
            echo ""
            test_fetch_from_web_dist
            ;;
        help)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo ""
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"