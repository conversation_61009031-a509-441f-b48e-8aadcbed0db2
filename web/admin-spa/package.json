{"name": "claude-relay-admin-spa", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.5.1", "axios": "^1.6.2", "chart.js": "^4.4.0", "dayjs": "^1.11.9", "element-plus": "^2.4.4", "pinia": "^2.1.7", "vue": "^3.3.4", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^10.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^3.3.6", "unplugin-auto-import": "^0.17.2", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.8", "vite-plugin-checker": "^0.10.2"}}