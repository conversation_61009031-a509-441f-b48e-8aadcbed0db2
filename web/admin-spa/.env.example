# ========== 基础配置 ==========

# 应用基础路径
# 用于配置路由和资源的基础路径
# 开发环境默认：/admin/
# 生产环境默认：/admin-next/
# 如果使用默认值，可以注释掉此行
VITE_APP_BASE_URL=/admin-next/

# 应用标题
# 显示在浏览器标签页和页面头部
VITE_APP_TITLE=Claude Relay Service - 管理后台

# ========== 开发环境配置 ==========

# API 代理目标地址
# 开发环境下，所有 /webapi 前缀的请求会被代理到这个地址
# 默认值：http://localhost:3000
# VITE_API_TARGET=http://localhost:3000

# HTTP 代理配置（可选）
# 如果需要通过代理访问后端服务器，请取消注释并配置
# 格式：http://proxy-host:port
#VITE_HTTP_PROXY=http://127.0.0.1:7890

# ========== 使用说明 ==========
# 1. 复制此文件为 .env.local 进行本地配置
# 2. .env.local 文件不会被提交到版本控制
# 3. 详细说明请查看 ENV_CONFIG.md