# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env.local
.env.*.local

# Logs
logs/
*.log

# Data files
data/
temp/

# Git
.git/
.gitignore
.gitattributes

# GitHub
.github/

# Documentation
README.md
README_EN.md
CHANGELOG.md
docs/
*.md

# Development files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Docker files
docker-compose.yml
docker-compose.*.yml
Dockerfile
.dockerignore

# Test files
test/
tests/
__tests__/
*.test.js
*.spec.js
coverage/
.nyc_output/

# Build files
# dist/  # 前端构建阶段需要复制源文件，所以不能忽略
build/
*.pid
*.seed
*.pid.lock

# 但可以忽略本地已构建的 dist 目录
web/admin-spa/dist/

# CI/CD
.travis.yml
.gitlab-ci.yml
azure-pipelines.yml

# Package manager files
# package-lock.json  # 需要保留此文件以支持 npm ci
yarn.lock
pnpm-lock.yaml

# CLI
cli/
