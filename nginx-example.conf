# API 路由 - 允许访问
  location /api/ {
      proxy_pass http://127.0.0.1:10000;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;

      # SSE 支持（流式响应）
      proxy_buffering off;
      proxy_cache off;
      proxy_read_timeout 86400s;
      proxy_send_timeout 86400s;
      client_max_body_size 10m;
  }

  # Claude API 路由别名
  location /claude/ {
      proxy_pass http://127.0.0.1:10000;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;

      proxy_buffering off;
      proxy_cache off;
      proxy_read_timeout 86400s;
      proxy_send_timeout 86400s;
      client_max_body_size 10m;
  }

  # Gemini API 路由
  location /gemini/ {
      proxy_pass http://127.0.0.1:10000;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;

      proxy_buffering off;
      proxy_cache off;
      proxy_read_timeout 86400s;
      proxy_send_timeout 86400s;
      client_max_body_size 10m;
  }

  # OpenAI 兼容路由
  location /openai/ {
      proxy_pass http://127.0.0.1:10000;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;

      proxy_buffering off;
      proxy_cache off;
      proxy_read_timeout 86400s;
      proxy_send_timeout 86400s;
      client_max_body_size 10m;
  }

  # 健康检查端点
  location /health {
      proxy_pass http://127.0.0.1:10000;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
  }

  # 屏蔽所有管理界面路由
  location /admin {
      return 404;
  }

#  location /admin-next {
#      return 404;
#  }

  location /web {
      return 404;
  }

#  location /apiStats {
#      return 404;
#  }

  # 根路径返回 404 或自定义页面
  location / {
      return 404 '{"error": "Not Found", "message": "API endpoint not found"}';
      add_header Content-Type application/json;
  }
